{"name": "cohort", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/ankurdotio/cohort-project.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/ankurdotio/cohort-project/issues"}, "homepage": "https://github.com/ankurdotio/cohort-project#readme", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "dotenv": "^17.2.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.5"}}