const mongoose  = require('mongoose');


function connectDB() {
    mongoose.connect(process.env.MONGODB_URL)
    .then(()=>{
         console.log('MongoDB connected successfully');
    })
    .catch((error) => {
        console.error('MongoDB connection error:', error.message);
        console.log('Please make sure MongoDB is running or update your connection string');
    })
}

module.exports = connectDB